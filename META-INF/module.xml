<?xml version="1.0" encoding="UTF-8"?>
<module name="datasyncTest-wb" bajaVersion="0" vendor="mea" vendorVersion="1.0" description="Example Driver Module" preferredSymbol="dst" nre="true" autoload="true" installable="true" buildMillis="1749993032604" buildHost="LAPTOP-B7S2HB0I" moduleName="datasyncTest" runtimeProfile="wb">
 <dependencies>
  <dependency name="alarm-rt" vendor="Tridium" vendorVersion="4.13"/>
  <dependency name="baja" vendor="Tridium" vendorVersion="4.13"/>
  <dependency name="bajaui-wb" vendor="Tridium" vendorVersion="4.13"/>
  <dependency name="basicDriver-rt" vendor="Tridium" vendorVersion="4.13"/>
  <dependency name="control-rt" vendor="Tridium" vendorVersion="4.13"/>
  <dependency name="driver-rt" vendor="Tridium" vendorVersion="4.13"/>
  <dependency name="driver-wb" vendor="Tridium" vendorVersion="4.13"/>
  <dependency name="gx-rt" vendor="Tridium" vendorVersion="4.13"/>
  <dependency name="gx-wb" vendor="Tridium" vendorVersion="4.13"/>
  <dependency name="workbench-wb" vendor="Tridium" vendorVersion="4.13"/>
 </dependencies>
 <dirs/>
 <installation/>
 <types>
  <type class="com.mea.datasynctest.ui.BDataSyncTool" name="DataSyncTool"/>
  <type class="com.examples.envctrldriver.ui.BEnvCtrlDeviceManager" name="EnvCtrlDeviceManager">
   <agent>
    <on type="envCtrlDriver:EnvCtrlDeviceNetwork"/>
    <on type="envCtrlDriver:EnvCtrlDeviceFolder"/>
   </agent>
  </type>
  <type class="com.examples.envctrldriver.ui.BEnvCtrlPointManager" name="EnvCtrlPointManager">
   <agent>
    <on type="envCtrlDriver:EnvCtrlPointDeviceExt"/>
    <on type="envCtrlDriver:EnvCtrlPointFolder"/>
   </agent>
  </type>
 </types>
</module>
