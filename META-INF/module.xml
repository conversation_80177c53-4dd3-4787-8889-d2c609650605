<?xml version="1.0" encoding="UTF-8"?>
<module name="datasync-wb" bajaVersion="0" vendor="mea" vendorVersion="1.0" description="Workbench data synchronization tool" preferredSymbol="ds" nre="true" autoload="true" installable="true" buildMillis="1749989139543" buildHost="LAPTOP-B7S2HB0I" moduleName="datasync" runtimeProfile="wb">
 <dependencies>
  <dependency name="bacnet-rt" vendor="Tridium" vendorVersion="4.13"/>
  <dependency name="bacnet-ux" vendor="Tridium" vendorVersion="4.13"/>
  <dependency name="bacnet-wb" vendor="Tridium" vendorVersion="4.13"/>
  <dependency name="baja" vendor="Tridium" vendorVersion="4.13"/>
  <dependency name="bajaui-ux" vendor="Tridium" vendorVersion="4.13"/>
  <dependency name="bajaui-wb" vendor="Tridium" vendorVersion="4.13"/>
  <dependency name="bajaux-rt" vendor="Tridium" vendorVersion="4.13"/>
  <dependency name="bajaux-ux" vendor="Tridium" vendorVersion="4.13"/>
  <dependency name="control-rt" vendor="Tridium" vendorVersion="4.13"/>
  <dependency name="control-ux" vendor="Tridium" vendorVersion="4.13"/>
  <dependency name="control-wb" vendor="Tridium" vendorVersion="4.13"/>
  <dependency name="gx-rt" vendor="Tridium" vendorVersion="4.13"/>
  <dependency name="gx-ux" vendor="Tridium" vendorVersion="4.13"/>
  <dependency name="gx-wb" vendor="Tridium" vendorVersion="4.13"/>
  <dependency name="workbench-wb" vendor="Tridium" vendorVersion="4.13"/>
 </dependencies>
 <dirs/>
 <installation/>
 <module description="Niagara 4 Data Synchronization Tool" name="datasync" preferredSymbol="nds" runtimeProfile="wb" vendor="mea" vendorVersion="1.0.0">
  <dirs>
   <dir name="com/mea/datasync/ui"/>
  </dirs>
  <types>
   <type class="com.mea.datasync.ui.BTestType" name="TestType"/>
   <type class="com.mea.datasync.ui.BDataSyncTool" name="DataSyncTool">
    <agent>
     <on type="workbench:Workbench"/>
    </agent>
   </type>
  </types>
  <lexicons>
   <lexicon language="en" module="datasync" resource="module.lexicon"/>
  </lexicons>
  <dependencies>
   <dependency name="baja" vendor="Tridium" vendorVersion="4.0"/>
   <dependency name="bajaui" vendor="Tridium" vendorVersion="4.0"/>
   <dependency name="workbench" vendor="Tridium" vendorVersion="4.0"/>
   <dependency name="control" vendor="Tridium" vendorVersion="4.0"/>
   <dependency name="bacnet" vendor="Tridium" vendorVersion="4.0"/>
   <dependency name="sys" vendor="Tridium" vendorVersion="4.0"/>
  </dependencies>
 </module>
 <permissions>
  <niagara-permission-groups type="all"/>
  <niagara-permission-groups type="workbench"/>
  <niagara-permission-groups type="station"/>
 </permissions>
</module>
