<?xml version="1.0" encoding="UTF-8"?>
<module name="jsonToolkit-wb" bajaVersion="0" vendor="Tridium" vendorVersion="*********" description="Utilities to help with Niagara/Json assembly and serialization" preferredSymbol="jstk" nre="true" autoload="true" installable="true" buildMillis="1714696367806" buildHost="883e7f7a9875" moduleName="jsonToolkit" runtimeProfile="wb" releaseDate="2023-07-17">
 <dependencies>
  <dependency name="alarm-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="backup-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="backup-wb" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="baja" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="bajaScript-ux" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="bajaui-ux" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="bajaui-wb" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="bajaux-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="bajaux-ux" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="box-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="bql-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="chart-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="chart-wb" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="control-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="converters-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="converters-ux" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="driver-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="driver-wb" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="entityIo-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="export-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="export-ux" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="export-wb" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="file-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="fox-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="gx-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="gx-ux" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="gx-wb" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="history-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="history-wb" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="hx-wb" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="jetty-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="js-ux" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="jsonSmart-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="jsonToolkit-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="ndriver-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="neql-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="net-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="nsh-wb" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="pdf-wb" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="platform-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="program-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="pxEditor-wb" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="query-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="queryTable-wb" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="raster-wb" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="schedule-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="schedule-wb" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="search-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="serial-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="smartTableHx-wb" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="tagdictionary-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="template-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="template-wb" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="uxBuilder-ux" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="wbutil-wb" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="web-rt" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="webEditors-ux" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="wiresheet-wb" vendor="Tridium" vendorVersion="4.13.3"/>
  <dependency name="workbench-wb" vendor="Tridium" vendorVersion="4.13.3"/>
 </dependencies>
 <dirs/>
 <installation/>
 <types>
  <type class="com.tridiumx.jsonToolkit.ui.BJsonOutputWidget" name="JsonOutputWidget"/>
  <type class="com.tridiumx.jsonToolkit.ui.fe.BQueryPickerFE" name="QueryPickerFE"/>
  <type class="com.tridiumx.jsonToolkit.ui.fe.BQueryStylePickerFE" name="QueryStylePickerFE"/>
  <type class="com.tridiumx.jsonToolkit.ui.fe.BSelectedSlotsFE" name="SelectedSlotsFE">
   <agent>
    <on type="jsonToolkit:SlotSelectionType"/>
   </agent>
  </type>
  <type class="com.tridiumx.jsonToolkit.ui.fe.BSlotOrdFE" name="SlotOrdFE"/>
  <type class="com.tridiumx.jsonToolkit.ui.fe.BTagNamespaceFE" name="TagNamespaceFE"/>
 </types>
 <permissions>
  <java-permissions type="workbench">
   <java-permission class="java.awt.AWTPermission" name="accessClipboard"/>
   <java-permission class="java.awt.AWTPermission" name="showWindowWithoutWarningBanner"/>
  </java-permissions>
 </permissions>
</module>
